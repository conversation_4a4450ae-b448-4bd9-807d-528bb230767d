# Cheat Engine 基址和偏移查找教程

## 前提条件
- 你已经找到了目标数值的地址
- 游戏或程序正在运行中

## 步骤1：准备工作
1. 打开Cheat Engine
2. 附加到目标进程
3. 确认你已经找到了目标数值的地址（比如：0x12345678）

## 步骤2：查找指向该地址的指针
1. 右键点击你找到的地址
2. 选择 "查找访问此地址的代码" 或 "Find out what accesses this address"
3. 在弹出的窗口中点击 "是" 开始监控
4. 回到游戏中，让这个数值发生变化（比如移动角色、使用物品等）
5. 观察CE中出现的汇编代码指令

## 步骤3：分析汇编指令
查找类似这样的指令：
```assembly
mov eax,[esi+0C]     ; 这表示从 esi+0C 地址读取数值
mov [ebx+14],eax     ; 这表示将数值写入 ebx+14 地址
```

## 步骤4：使用指针扫描
1. 右键点击目标地址
2. 选择 "指针扫描" 或 "Pointer scan for this address"
3. 设置扫描参数：
   - 最大偏移级别：通常设置为3-5级
   - 最大偏移：通常设置为4096或8192
4. 点击 "确定" 开始扫描

## 步骤5：指针扫描后的详细操作

### 5.1 理解扫描结果
扫描完成后，会弹出一个新窗口显示找到的指针路径，每一行代表一个可能的指针链：

```
地址                    偏移
game.exe+001A2B40  ->  +4C -> +18 -> +0
game.exe+001A2B40  ->  +4C -> +20
module.dll+00345678 ->  +8 -> +14 -> +0
...
```

### 5.2 选择合适的指针
1. **优先选择模块基址**：以 `.exe` 或 `.dll` 结尾的地址更稳定
2. **偏移层级较少**：通常2-4级偏移比较理想
3. **避免过大偏移**：偏移值过大（如 +FFFF）可能不稳定

### 5.3 添加指针到地址列表
1. **双击选中的指针行**，或右键选择 "添加到地址列表"
2. 在弹出的对话框中：
   - 描述：输入有意义的名称（如 "玩家血量"）
   - 类型：选择 Double（与原地址一致）
   - 确认指针路径正确
3. 点击 "确定" 添加

### 5.4 验证指针有效性
**第一次验证（当前会话）：**
1. 在地址列表中查看新添加的指针
2. 确认显示的值是否为 120（与原地址一致）
3. 在游戏中改变这个数值
4. 观察指针地址的值是否同步变化

**第二次验证（重启后）：**
1. **完全关闭游戏**
2. **重新启动游戏**
3. **重新附加 CE 到游戏进程**
4. 检查指针是否仍然指向正确的值

### 5.5 处理验证结果

**如果指针有效：**
- 记录完整的指针路径
- 可以删除原来的静态地址
- 保存 CE 表格文件以备后用

**如果指针失效：**
1. 尝试其他扫描结果中的指针
2. 重新进行指针扫描，调整参数：
   - 增加最大偏移级别到 6-7
   - 增加最大偏移到 8192 或更大
3. 考虑使用手动查找方法

### 5.6 多指针备选方案
建议保留 2-3 个有效的指针作为备选：
1. 主指针：最稳定的那个
2. 备用指针1：不同模块或偏移路径
3. 备用指针2：作为最后的备选

### 5.7 指针路径记录格式
记录格式示例：
```
主指针：game.exe+001A2B40 -> +4C -> +18 -> +0
备用1：game.exe+001A2B40 -> +4C -> +20
备用2：module.dll+00345678 -> +8 -> +14 -> +0
```

## 步骤6：手动查找基址
如果自动扫描效果不好，可以手动查找：

1. 在内存查看器中查看你的地址
2. 查找指向该地址的指针（通常是4字节对齐的地址）
3. 继续向上查找，直到找到一个相对稳定的基址

## 常见的基址特征
- 通常以模块名开头（如：game.exe+偏移）
- 地址相对固定，重启程序后变化不大
- 经常是绿色显示的地址（表示是模块基址）

## 实用技巧

### 技巧1：使用内存查看器
1. 右键目标地址 → "浏览此内存区域"
2. 在内存查看器中查看周围的数据结构
3. 寻找可能的对象或结构体

### 技巧2：查找代码注入点
1. 找到访问目标地址的汇编代码
2. 记录寄存器的值（如ESI, EBX等）
3. 这些寄存器可能包含对象的基址

### 技巧3：多级指针
如果是多级指针，格式通常是：
```
基址 → 偏移1 → 偏移2 → 偏移3 → 目标值
```

## 示例：假设你找到了血量地址
1. 血量地址：0x12345678
2. 通过指针扫描找到：game.exe+0x123456 → +0x4C → +0x18
3. 验证：重启游戏后这个指针路径仍然有效

## 注意事项
- 不同的游戏和程序结构不同，方法可能需要调整
- 有些程序使用动态内存分配，基址可能经常变化
- 建议保存多个可能的指针路径作为备选

## 实际操作示例

### 示例：处理您的地址 2BD89238
假设您的指针扫描结果如下：

```
扫描结果窗口显示：
序号  地址                    偏移链
1     game.exe+001A2B40  ->  +4C -> +18 -> +0
2     game.exe+001A2B40  ->  +4C -> +20
3     game.exe+001B5678  ->  +8 -> +14 -> +0
4     module.dll+00345678 -> +10 -> +8 -> +4 -> +0
```

**操作步骤：**
1. **选择指针1**（偏移较少，基于主模块）
2. **双击第1行**，弹出添加对话框
3. **填写信息**：
   - 描述：玩家数值_120
   - 类型：Double
   - 指针路径自动填充
4. **点击确定**添加到地址列表
5. **验证**：确认新指针显示值为 120

### 重启验证流程
1. **保存CE表格**：File -> Save As -> 保存为 .CT 文件
2. **关闭游戏**
3. **重启游戏**
4. **重新附加CE**
5. **加载保存的表格**：File -> Load
6. **检查指针值**：
   - ✅ 如果仍显示正确值 → 指针有效
   - ❌ 如果显示 "?" 或错误值 → 指针失效

### 失效时的处理
如果指针1失效，依次尝试：
1. **测试指针2**：双击添加，验证
2. **测试指针3**：双击添加，验证
3. **重新扫描**：使用不同参数
4. **手动查找**：使用方法二或方法三

## 故障排除
- 如果指针失效，可能是游戏更新了
- 尝试重新扫描或使用不同的扫描参数
- 检查是否有反作弊保护影响扫描结果
- 某些游戏使用动态内存分配，需要更复杂的查找方法