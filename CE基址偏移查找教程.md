# Cheat Engine 基址和偏移查找教程

## 前提条件
- 你已经找到了目标数值的地址
- 游戏或程序正在运行中

## 步骤1：准备工作
1. 打开Cheat Engine
2. 附加到目标进程
3. 确认你已经找到了目标数值的地址（比如：0x12345678）

## 步骤2：查找指向该地址的指针
1. 右键点击你找到的地址
2. 选择 "查找访问此地址的代码" 或 "Find out what accesses this address"
3. 在弹出的窗口中点击 "是" 开始监控
4. 回到游戏中，让这个数值发生变化（比如移动角色、使用物品等）
5. 观察CE中出现的汇编代码指令

## 步骤3：分析汇编指令
查找类似这样的指令：
```assembly
mov eax,[esi+0C]     ; 这表示从 esi+0C 地址读取数值
mov [ebx+14],eax     ; 这表示将数值写入 ebx+14 地址
```

## 步骤4：使用指针扫描
1. 右键点击目标地址
2. 选择 "指针扫描" 或 "Pointer scan for this address"
3. 设置扫描参数：
   - 最大偏移级别：通常设置为3-5级
   - 最大偏移：通常设置为4096或8192
4. 点击 "确定" 开始扫描

## 步骤5：验证指针
1. 扫描完成后会显示可能的指针路径
2. 重启游戏或程序
3. 在CE中测试这些指针是否仍然指向正确的地址
4. 选择稳定的指针路径

## 步骤6：手动查找基址
如果自动扫描效果不好，可以手动查找：

1. 在内存查看器中查看你的地址
2. 查找指向该地址的指针（通常是4字节对齐的地址）
3. 继续向上查找，直到找到一个相对稳定的基址

## 常见的基址特征
- 通常以模块名开头（如：game.exe+偏移）
- 地址相对固定，重启程序后变化不大
- 经常是绿色显示的地址（表示是模块基址）

## 实用技巧

### 技巧1：使用内存查看器
1. 右键目标地址 → "浏览此内存区域"
2. 在内存查看器中查看周围的数据结构
3. 寻找可能的对象或结构体

### 技巧2：查找代码注入点
1. 找到访问目标地址的汇编代码
2. 记录寄存器的值（如ESI, EBX等）
3. 这些寄存器可能包含对象的基址

### 技巧3：多级指针
如果是多级指针，格式通常是：
```
基址 → 偏移1 → 偏移2 → 偏移3 → 目标值
```

## 示例：假设你找到了血量地址
1. 血量地址：0x12345678
2. 通过指针扫描找到：game.exe+0x123456 → +0x4C → +0x18
3. 验证：重启游戏后这个指针路径仍然有效

## 注意事项
- 不同的游戏和程序结构不同，方法可能需要调整
- 有些程序使用动态内存分配，基址可能经常变化
- 建议保存多个可能的指针路径作为备选

## 故障排除
- 如果指针失效，可能是游戏更新了
- 尝试重新扫描或使用不同的扫描参数
- 检查是否有反作弊保护影响扫描结果