// ==UserScript==
// @name         腾讯质检平台 - 全能自动处理 (问题1&8版)
// @namespace    http://tampermonkey.net/
// @version      18.0
// @description  【问题1&8版 v18.0】修改为只处理问题1和问题8，简化操作界面，提高处理效率。
// <AUTHOR> AI (Final UI-Fix by AI Assistant)
// @match        https://qlabel.tencent.com/workbench/tasks*
// @match        https://qlabel.tencent.com/workbench/*
// @grant        GM_addStyle
// ==/UserScript==

(function() {
    'use strict';
    
    // 调试信息 - 脚本开始执行
    console.log('[DEBUG v18.0] 脚本开始执行');
    console.log('[DEBUG v18.0] 当前URL:', window.location.href);
    console.log('[DEBUG v18.0] 页面标题:', document.title);

    // --- Konfiguration ---
    const MIN_DELAY = 1000;
    const MAX_DELAY = 3000;
    const DEFAULT_CHOICES = ['1', '0']; // 问题1和问题8的默认选择
    let currentMode = 'modify'; // 'modify' or 'auto-pass'

    // --- Globale Variablen ---
    let isAutomationRunning = false;
    let statusIndicator = null;
    let continuousButton = null;
    let semiAutoButton = null;
    let questionConfigPanel = null;
    let checkTaskIntervalId = null;
    let processTimeoutId = null;
    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };

    // --- Hilfsfunktionen ---
    function findButtonByText(text) {
        return Array.from(document.querySelectorAll('button, a'))
                    .find(el => el.textContent.trim().includes(text));
    }

    function updateStatus(message, color = '#6b7280') {
        if (statusIndicator) {
            let statusIcon = '🎯';
            if (color === 'green') statusIcon = '✅';
            else if (color === 'red') statusIcon = '❌';
            else if (color === '#6b7280') statusIcon = '🎯';
            
            statusIndicator.innerHTML = `<span class="status-icon">${statusIcon}</span><span class="status-text">${message}</span>`;
            statusIndicator.style.color = color;
        }
        console.log(`[STATUS v18.0] ${message}`);
    }

    function setButtonsDisabled(disabled) {
        if (continuousButton) continuousButton.disabled = disabled;
        if (semiAutoButton) semiAutoButton.disabled = disabled;
    }

    function dispatchRealClick(element) {
        if (!element) return false;
        try {
            const event = new MouseEvent('click', { bubbles: true, cancelable: true });
            element.dispatchEvent(event);
            return true;
        } catch (e) {
            console.error('[DEBUG] FEHLER beim Auslösen von Klick-Ereignissen:', e);
            updateStatus(`致命错误: 无法点击元素。请查看F12控制台。`, 'red');
            return false;
        }
    }

    // --- Kernfunktionen ---
    function performProcessingSequence(onComplete) {
        // Deaktiviert beide Knöpfe nur während der kurzen Ausführung
        setButtonsDisabled(true);
        step1_clickModifyAnnotation(onComplete);
    }

    function step1_clickModifyAnnotation(onComplete) {
        updateStatus('步骤 1: 点击“修改标注”...');
        const modifyButton = findButtonByText('修改标注');
        if (!modifyButton) { updateStatus('错误: 未找到“修改标注”按钮!', 'red'); stopAutomation(); return; }
        modifyButton.click();
        setTimeout(() => step2_selectOptions(onComplete), 700);
    }

    function step2_selectOptions(onComplete) {
        updateStatus('步骤 2: 检查并应用所选选项...');
        const questionBlocks = document.querySelectorAll('.ivu-card-body .ivu-form-item');
        if (questionBlocks.length < 2) { updateStatus('错误: 未能找到所有2个问题区块！', 'red'); stopAutomation(); return; }

        const questionIndices = [1, 8]; // 对应问题1和问题8
        for (let i = 0; i < 2; i++) {
            const questionNum = questionIndices[i];
            const enableCheckbox = document.getElementById(`q${questionNum}_enable`);
            if (!enableCheckbox || !enableCheckbox.checked) { continue; }
            
            const choiceRadio = document.querySelector(`input[name="q${questionNum}_options"]:checked`);
            if (!choiceRadio) { updateStatus(`错误: 问题 ${questionNum} 已启用但未选择1或0!`, 'red'); stopAutomation(); return; }
            const choiceText = choiceRadio.value;

            let optionFound = false;
            const block = questionBlocks[i];
            const wrapperOptions = block.querySelectorAll('.ivu-checkbox-wrapper, .ivu-radio-wrapper');
            for (const wrapper of wrapperOptions) {
                for (const node of wrapper.childNodes) {
                    if (node.nodeType === Node.TEXT_NODE && node.textContent.trim() === choiceText) {
                        optionFound = true;
                        const isAlreadyChecked = wrapper.classList.contains('ivu-checkbox-wrapper-checked') || wrapper.classList.contains('ivu-radio-wrapper-checked');
                        if (!isAlreadyChecked) {
                            const inputElement = wrapper.querySelector('input[type="checkbox"], input[type="radio"]');
                            if (inputElement && !dispatchRealClick(inputElement)) {
                                stopAutomation(); return;
                            }
                        }
                        break;
                    }
                }
                if (optionFound) break;
            }
            if (!optionFound) { updateStatus(`错误: 问题 ${questionNum} 的选项 "${choiceText}" 未找到!`, 'red'); stopAutomation(); return; }
        }
        setTimeout(() => step3_clickSubmitModification(onComplete), 500);
    }

    function step3_clickSubmitModification(onComplete) {
        updateStatus('步骤 3: 点击“提交修改”...');
        const submitButton = findButtonByText('提交修改');
        if (!submitButton) { updateStatus('错误: 未找到“提交修改”按钮!', 'red'); stopAutomation(); return; }
        submitButton.click();
        step4_clickFinalConfirm(onComplete, 0);
    }

    function step4_clickFinalConfirm(onComplete, attempts) {
        if (attempts >= 15) { updateStatus('超时: 未找到最终确认按钮。', 'red'); stopAutomation(); return; }
        const confirmButton = document.querySelector('.ivu-modal-footer .ivu-btn-primary');
        if (confirmButton) {
            updateStatus('步骤 4: 点击最终确认按钮!');
            confirmButton.click();
            // 如果启用了自动通过功能，等待一段时间后自动点击质检通过
            if (typeof onComplete === 'function') {
                onComplete();
            } else {
                updateStatus('单次处理完成，待命中。', 'green');
                setButtonsDisabled(false);
            }
        } else {
            setTimeout(() => step4_clickFinalConfirm(onComplete, attempts + 1), 200);
        }
    }

    function step5_clickQualityPass(onComplete, attempts = 0) {
        if (attempts >= 20) {
            updateStatus('超时: 未找到"质检通过"按钮。', 'red');
            if (typeof onComplete === 'function') {
                onComplete();
            } else {
                setButtonsDisabled(false);
            }
            return;
        }

        // 查找质检通过按钮，支持多种可能的文本匹配
        const passButton = findButtonByText('质检通过') ||
                          findButtonByText('通过') ||
                          Array.from(document.querySelectorAll('button, a'))
                               .find(el => el.textContent.includes('质检通过') ||
                                          el.getAttribute('title')?.includes('质检通过') ||
                                          el.getAttribute('aria-label')?.includes('质检通过'));

        if (passButton) {
            updateStatus('步骤 5: 自动点击"质检通过"按钮!', 'green');
            if (dispatchRealClick(passButton)) {
                setTimeout(() => {
                    if (typeof onComplete === 'function') {
                        onComplete();
                    } else {
                        updateStatus('自动通过完成，待命中。', 'green');
                        setButtonsDisabled(false);
                    }
                }, 500);
            } else {
                updateStatus('错误: 无法点击质检通过按钮!', 'red');
                setButtonsDisabled(false);
            }
        } else {
            updateStatus(`等待质检通过按钮... (${attempts + 1}/20)`);
            setTimeout(() => step5_clickQualityPass(onComplete, attempts + 1), 500);
        }
    }

    // --- Automatisierungs-Controller ---
    function automationCycle() {
        if (!isAutomationRunning) {
            setButtonsDisabled(false);
            return;
        }

        if (currentMode === 'modify') {
            checkTaskIntervalId = setInterval(() => {
                if (findButtonByText('修改标注')) {
                    clearInterval(checkTaskIntervalId);
                    checkTaskIntervalId = null;

                    const randomDelay = MIN_DELAY + Math.random() * (MAX_DELAY - MIN_DELAY);
                    updateStatus(`检测到新任务，将在 ${Math.round(randomDelay / 1000)} 秒后处理...`);

                    processTimeoutId = setTimeout(() => {
                        if (!isAutomationRunning) return;
                        performProcessingSequence(() => {
                            updateStatus('任务处理完成，等待下一轮...', 'green');
                            setButtonsDisabled(true);
                            continuousButton.disabled = false;
                            automationCycle();
                        });
                    }, randomDelay);
                }
            }, 1000);
        } else if (currentMode === 'auto-pass') {
            checkTaskIntervalId = setInterval(() => {
                const passButton = findButtonByText('质检通过');
                if (passButton) {
                    clearInterval(checkTaskIntervalId);
                    checkTaskIntervalId = null;

                    const randomDelay = MIN_DELAY + Math.random() * (MAX_DELAY - MIN_DELAY);
                    updateStatus(`检测到“质检通过”按钮，将在 ${Math.round(randomDelay / 1000)} 秒后点击...`);

                    processTimeoutId = setTimeout(() => {
                        if (!isAutomationRunning) return;
                        step5_clickQualityPass(() => {
                            updateStatus('“质检通过”点击完成，等待下一轮...', 'green');
                            setButtonsDisabled(true);
                            continuousButton.disabled = false;
                            automationCycle();
                        });
                    }, randomDelay);
                }
            }, 1000);
        }
    }

    // ÜBERARBEITETE startAutomation-Funktion
    function startAutomation() {
        if (isAutomationRunning) return;
        isAutomationRunning = true;
        
        // UI aktualisieren
        continuousButton.innerHTML = '<span class="btn-icon">🛑</span><span class="btn-text">停止连续处理</span>';
        continuousButton.style.backgroundColor = '#d9363e';
        
        // **DIE ENTSCHEIDENDE KORREKTUR:**
        // Nur der Einzelverarbeitungs-Knopf wird deaktiviert.
        // Der kontinuierliche Knopf (jetzt der Stopp-Knopf) bleibt aktiv.
        continuousButton.disabled = false;
        semiAutoButton.disabled = true;
        
        updateStatus('连续处理已启动...', 'green');
        automationCycle();
    }

    // ÜBERARBEITETE stopAutomation-Funktion
    function stopAutomation() {
        isAutomationRunning = false;

        // Lösche alle laufenden Timer
        if (checkTaskIntervalId) {
            clearInterval(checkTaskIntervalId);
            checkTaskIntervalId = null;
        }
        if (processTimeoutId) {
            clearTimeout(processTimeoutId);
            processTimeoutId = null;
        }

        // UI zurücksetzen und ALLE Knöpfe aktivieren
        continuousButton.innerHTML = '<span class="btn-icon">🚀</span><span class="btn-text">启动连续处理</span>';
        continuousButton.style.backgroundColor = '#28a745';
        setButtonsDisabled(false); // Aktiviert beide Knöpfe
        
        if (statusIndicator && !statusIndicator.innerHTML.includes('错误')) {
            updateStatus('自动化已停止。', 'red');
        }
    }

    // --- 拖拽功能 ---
    function makeElementDraggable(element) {
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        element.addEventListener('mousedown', function(e) {
            // 只有点击标题栏或面板空白区域才能拖拽，避免与按钮冲突
            if (e.target.tagName === 'BUTTON' || e.target.tagName === 'INPUT' || e.target.tagName === 'LABEL') {
                return;
            }
            
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            
            const rect = element.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;
            
            element.style.cursor = 'grabbing';
            e.preventDefault();
        });

        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            const newLeft = startLeft + deltaX;
            const newTop = startTop + deltaY;
            
            // 限制拖拽范围，不让面板完全离开屏幕
            const maxLeft = window.innerWidth - element.offsetWidth;
            const maxTop = window.innerHeight - element.offsetHeight;
            
            element.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
            element.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
            element.style.right = 'auto'; // 移除right定位
        });

        document.addEventListener('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                element.style.cursor = 'grab';
            }
        });
    }

    // --- UI-Setup ---
    function createAutomationUI() {
        console.log('[DEBUG v18.0] createAutomationUI 被调用');
        if (document.getElementById('automation-control-panel')) {
            console.log('[DEBUG v18.0] 界面已存在，跳过创建');
            return;
        }
        console.log('[DEBUG v18.0] 开始创建界面');
        const panel = document.createElement('div');
        panel.id = 'automation-control-panel';
        
        // 添加拖拽标题栏
        const dragHandle = document.createElement('div');
        dragHandle.id = 'drag-handle';
        dragHandle.innerHTML = '<span class="drag-icon">🎯</span><span class="title-text">腾讯质检助手</span><span class="drag-hint">⋮⋮</span>';
        dragHandle.title = '点击此处拖拽面板';
        panel.appendChild(dragHandle);
        
        const configPanel = document.createElement('div');
        configPanel.id = 'processing-config-panel';
        configPanel.innerHTML = `
            <div id="mode-selector">
                <button id="mode-modify" class="mode-button active" data-mode="modify">
                    <span class="mode-icon">✏️</span>
                    <span class="mode-text">修改标注</span>
                </button>
                <button id="mode-auto-pass" class="mode-button" data-mode="auto-pass">
                    <span class="mode-icon">✅</span>
                    <span class="mode-text">自动通过</span>
                </button>
            </div>
        `;

        questionConfigPanel = document.createElement('div');
        questionConfigPanel.id = 'question-config-panel';

        const questionNumbers = [1, 8]; // 只显示问题1和问题8
        for (let i = 0; i < 2; i++) {
            const questionNum = questionNumbers[i];
            const choice = DEFAULT_CHOICES[i];
            const qRow = document.createElement('div');
            qRow.className = 'config-row';
            qRow.innerHTML = `
                <div class="question-card">
                    <div class="question-header">
                        <input type="checkbox" id="q${questionNum}_enable" class="enable-checkbox" title="勾选此项以修改问题${questionNum}" checked>
                        <label class="q-label" for="q${questionNum}_enable"><span class="q-number">Q${questionNum}</span>问题${questionNum}</label>
                    </div>
                    <div class="radio-group">
                        <div class="radio-option ${choice === '1' ? 'selected' : ''}">
                            <input type="radio" id="q${questionNum}_opt1" name="q${questionNum}_options" value="1" ${choice === '1' ? 'checked' : ''}>
                            <label for="q${questionNum}_opt1"><span class="option-badge">选项1</span></label>
                        </div>
                        <div class="radio-option ${choice === '0' ? 'selected' : ''}">
                            <input type="radio" id="q${questionNum}_opt0" name="q${questionNum}_options" value="0" ${choice === '0' ? 'checked' : ''}>
                            <label for="q${questionNum}_opt0"><span class="option-badge">选项0</span></label>
                        </div>
                    </div>
                </div>`;
            questionConfigPanel.appendChild(qRow);
        }
        configPanel.appendChild(questionConfigPanel);
        // 按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'button-container';
        
        continuousButton = document.createElement('button');
        continuousButton.innerHTML = '<span class="btn-icon">🚀</span><span class="btn-text">启动连续处理</span>';
        continuousButton.id = 'automation-toggle-button';
        continuousButton.addEventListener('click', () => isAutomationRunning ? stopAutomation() : startAutomation());
        
        semiAutoButton = document.createElement('button');
        semiAutoButton.innerHTML = '<span class="btn-icon">⚡️</span><span class="btn-text">单次处理</span>';
        semiAutoButton.id = 'semi-auto-button';
        semiAutoButton.addEventListener('click', () => {
            if (currentMode === 'modify') {
                performProcessingSequence(null);
            } else {
                step5_clickQualityPass(null);
            }
        });
        
        buttonContainer.appendChild(continuousButton);
        buttonContainer.appendChild(semiAutoButton);
        statusIndicator = document.createElement('div');
        statusIndicator.id = 'automation-status';
        statusIndicator.innerHTML = '<span class="status-icon">🎯</span><span class="status-text">待命中</span>';
        
        panel.appendChild(configPanel);
        panel.appendChild(document.createElement('hr'));
        panel.appendChild(buttonContainer);
        panel.appendChild(statusIndicator);
        document.body.appendChild(panel);
        
        // 添加模式切换事件监听器
        const modeModifyButton = document.getElementById('mode-modify');
        const modeAutoPassButton = document.getElementById('mode-auto-pass');

        function setMode(mode) {
            currentMode = mode;
            if (mode === 'modify') {
                modeModifyButton.classList.add('active');
                modeAutoPassButton.classList.remove('active');
                questionConfigPanel.style.display = 'block';
                semiAutoButton.style.display = 'block';
                updateStatus('切换到修改标注模式', 'green');
            } else {
                modeModifyButton.classList.remove('active');
                modeAutoPassButton.classList.add('active');
                questionConfigPanel.style.display = 'none';
                semiAutoButton.style.display = 'none'; // Hide single-pass button in auto-pass mode
                updateStatus('切换到自动通过模式', 'green');
            }
        }

        modeModifyButton.addEventListener('click', () => setMode('modify'));
        modeAutoPassButton.addEventListener('click', () => setMode('auto-pass'));
        
        // 启用拖拽功能
        makeElementDraggable(panel);
        
        updateStatus('待命中。');
        GM_addStyle(`
            #automation-control-panel {
                position: fixed; top: 80px; right: 15px; z-index: 9999;
                background: linear-gradient(145deg, #ffffff, #f8f9fa);
                border: 1px solid #e3e6ea; border-radius: 12px; padding: 0;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                box-shadow: 0 8px 25px rgba(0,0,0,0.12), 0 4px 10px rgba(0,0,0,0.08);
                display: flex; flex-direction: column; width: 280px;
                cursor: grab; user-select: none; backdrop-filter: blur(10px);
            }
            #automation-control-panel:active {
                cursor: grabbing;
            }
            #drag-handle {
                background: linear-gradient(135deg, #4f46e5, #7c3aed);
                color: white; padding: 12px 16px; font-size: 13px; font-weight: 600;
                border-top-left-radius: 12px; border-top-right-radius: 12px;
                cursor: grab; border-bottom: 1px solid rgba(255,255,255,0.2);
                display: flex; align-items: center; justify-content: space-between;
                box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
            }
            #drag-handle:active {
                cursor: grabbing;
            }
            .drag-icon, .drag-hint {
                font-size: 14px; opacity: 0.9;
            }
            .title-text {
                flex: 1; text-align: center; font-weight: 700;
            }
            #processing-config-panel {
                padding: 20px; display: flex; flex-direction: column; gap: 16px;
            }
            #mode-selector {
                display: flex;
                justify-content: center;
                margin-bottom: 16px;
                background-color: #e9ecef;
                border-radius: 8px;
                padding: 4px;
            }
            .mode-button {
                flex: 1;
                padding: 8px 12px;
                border: none;
                background-color: transparent;
                color: #495057;
                font-size: 13px;
                font-weight: 600;
                cursor: pointer;
                border-radius: 6px;
                transition: all 0.2s ease-in-out;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 6px;
            }
            .mode-button:hover {
                background-color: #dee2e6;
            }
            .mode-button.active {
                background-color: #ffffff;
                color: #4f46e5;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            #question-config-panel {
                display: flex;
                flex-direction: column;
                gap: 16px;
            }
            #config-title {
                font-weight: 700; text-align: center; font-size: 15px;
                color: #374151; margin-bottom: 8px; display: flex;
                align-items: center; justify-content: center; gap: 8px;
            }
            .config-icon {
                font-size: 16px;
            }
            .config-row {
                margin-bottom: 12px;
            }
            .question-card {
                background: linear-gradient(145deg, #f9fafb, #ffffff);
                border: 1px solid #e5e7eb; border-radius: 8px; padding: 12px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                transition: all 0.2s ease;
            }
            .question-card:hover {
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            .question-header {
                display: flex; align-items: center; gap: 8px; margin-bottom: 10px;
            }
            .enable-checkbox {
                cursor: pointer; transform: scale(1.2);
                accent-color: #4f46e5;
            }
            .q-label {
                font-size: 14px; cursor: pointer; font-weight: 600;
                color: #374151; display: flex; align-items: center; gap: 6px;
            }
            .q-number {
                background: linear-gradient(135deg, #4f46e5, #7c3aed);
                color: white; font-size: 11px; padding: 2px 6px;
                border-radius: 4px; font-weight: 700;
            }
            .radio-group {
                display: flex; gap: 8px; justify-content: center;
            }
            .radio-option {
                display: flex; align-items: center; gap: 4px;
                padding: 6px 12px; border-radius: 6px; cursor: pointer;
                transition: all 0.2s ease; border: 1px solid #e5e7eb;
                background: #ffffff;
            }
            .radio-option:hover {
                background: #f3f4f6; border-color: #d1d5db;
            }
            .radio-option.selected {
                background: linear-gradient(135deg, #ddd6fe, #e0e7ff);
                border-color: #7c3aed; box-shadow: 0 2px 4px rgba(124, 58, 237, 0.2);
            }
            .radio-option input {
                cursor: pointer; accent-color: #4f46e5;
            }
            .radio-option label {
                cursor: pointer; font-size: 13px; font-weight: 500;
                color: #374151; margin: 0;
            }
            .option-badge {
                padding: 2px 8px; border-radius: 4px; font-size: 11px;
                background: #f3f4f6; color: #6b7280; font-weight: 600;
            }
            .radio-option.selected .option-badge {
                background: #4f46e5; color: white;
            }
            #automation-control-panel hr {
                margin: 0 20px; border: none; border-top: 1px solid #e5e7eb;
                opacity: 0.6;
            }
            .button-container {
                padding: 0 20px 16px 20px; display: flex; flex-direction: column; gap: 10px;
            }
            #automation-toggle-button, #semi-auto-button {
                color: white; border: none; border-radius: 8px; padding: 12px 16px;
                font-size: 14px; font-weight: 600; cursor: pointer; width: 100%;
                transition: all 0.3s ease; display: flex; align-items: center;
                justify-content: center; gap: 8px; position: relative; overflow: hidden;
            }
            #automation-toggle-button {
                background: linear-gradient(135deg, #10b981, #059669);
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            }
            #automation-toggle-button:hover {
                transform: translateY(-1px); box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
            }
            #semi-auto-button {
                background: linear-gradient(135deg, #3b82f6, #2563eb);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }
            #semi-auto-button:hover {
                transform: translateY(-1px); box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
            }
            #automation-toggle-button:disabled, #semi-auto-button:disabled {
                opacity: 0.6; cursor: not-allowed; transform: none;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .btn-icon {
                font-size: 16px;
            }
            .btn-text {
                font-weight: 600;
            }
            #automation-status {
                padding: 0 20px 20px 20px; text-align: center; font-size: 13px;
                font-weight: 600; color: #6b7280; display: flex; align-items: center;
                justify-content: center; gap: 6px; background: #f9fafb;
                margin: 0 12px 12px 12px; border-radius: 6px; padding: 10px;
                border: 1px solid #e5e7eb;
            }
            .status-icon {
                font-size: 14px;
            }
            .status-text {
                font-weight: 600;
            }
        `);
    }

    console.log('[DEBUG v18.0] 设置定时器，每2秒检查一次界面');
    setInterval(createAutomationUI, 2000);
})();