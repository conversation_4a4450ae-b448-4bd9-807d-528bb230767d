// ==UserScript==
// @name         腾讯质检平台 - 全能自动处理 (修复版)
// @namespace    http://tampermonkey.net/
// @version      19.1
// @description  【修复版 v19.1】修复按钮点击问题，保持与原版本相同的点击逻辑
// <AUTHOR> Assistant (Fixed Version)
// @match        https://qlabel.tencent.com/workbench/tasks*
// @match        https://qlabel.tencent.com/workbench/*
// @grant        GM_addStyle
// @grant        GM_setValue
// @grant        GM_getValue
// ==/UserScript==

(function() {
    'use strict';
    
    // ===== 配置管理 =====
    const CONFIG = {
        MIN_DELAY: 1000,
        MAX_DELAY: 3000,
        DEFAULT_CHOICES: ['1', '0'],
        MAX_RETRIES: 15,
        CHECK_INTERVAL: 1000
    };
    
    // ===== 全局状态 =====
    let isAutomationRunning = false;
    let currentMode = GM_getValue('currentMode', 'modify');
    let statusIndicator = null;
    let continuousButton = null;
    let singleButton = null;
    let checkTimer = null;
    let processTimer = null;
    
    // ===== 工具函数 =====
    function log(message, level = 'INFO') {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${level} v19.1 ${timestamp}] ${message}`);
    }
    
    function findButtonByText(text) {
        return Array.from(document.querySelectorAll('button, a'))
                    .find(el => el.textContent.trim().includes(text));
    }
    
    function updateStatus(message, type = 'info') {
        if (!statusIndicator) return;
        
        const icons = { info: '🎯', success: '✅', warning: '⚠️', error: '❌' };
        const colors = { info: '#6b7280', success: '#10b981', warning: '#f59e0b', error: '#ef4444' };
        
        statusIndicator.innerHTML = `
            <span class="status-icon">${icons[type]}</span>
            <span class="status-text">${message}</span>
        `;
        statusIndicator.style.color = colors[type];
        log(`状态: ${message}`);
    }
    
    function getRandomDelay() {
        return CONFIG.MIN_DELAY + Math.random() * (CONFIG.MAX_DELAY - CONFIG.MIN_DELAY);
    }
    
    // ===== 核心处理函数 =====
    async function processTask() {
        try {
            updateStatus('开始处理任务...', 'info');
            
            if (currentMode === 'modify') {
                await processModification();
            } else {
                await processAutoPass();
            }
            
            updateStatus('任务处理完成', 'success');
        } catch (error) {
            log(`处理失败: ${error.message}`, 'ERROR');
            updateStatus(`处理失败: ${error.message}`, 'error');
            
            if (isAutomationRunning) {
                // 如果是自动模式，重新开始监控
                setTimeout(startMonitoring, 2000);
            }
        }
    }
    
    async function processModification() {
        // 步骤1: 点击修改标注
        log('步骤1: 查找修改标注按钮...');
        const modifyButton = findButtonByText('修改标注');
        if (!modifyButton) {
            // 调试信息：列出所有按钮
            const allButtons = document.querySelectorAll('button, a');
            log(`当前页面共有 ${allButtons.length} 个按钮/链接:`);
            allButtons.forEach((btn, index) => {
                log(`  按钮${index}: "${btn.textContent.trim()}"`);
            });
            throw new Error('未找到"修改标注"按钮');
        }
        
        log(`找到修改标注按钮: "${modifyButton.textContent.trim()}"`);
        updateStatus('点击修改标注按钮...', 'info');
        
        // 使用原版本的点击方法
        modifyButton.click();
        await delay(700);
        
        // 步骤2: 选择选项
        log('步骤2: 选择选项...');
        updateStatus('选择选项...', 'info');
        await selectOptions();
        await delay(500);
        
        // 步骤3: 提交修改
        log('步骤3: 提交修改...');
        updateStatus('提交修改...', 'info');
        const submitButton = findButtonByText('提交修改');
        if (!submitButton) {
            throw new Error('未找到"提交修改"按钮');
        }
        submitButton.click();
        
        // 步骤4: 确认提交
        log('步骤4: 等待确认按钮...');
        updateStatus('等待确认按钮...', 'info');
        await waitForConfirmButton();
    }
    
    async function selectOptions() {
        const questionBlocks = document.querySelectorAll('.ivu-card-body .ivu-form-item');
        if (questionBlocks.length < 2) {
            throw new Error('未找到足够的问题区块');
        }
        
        const questionIndices = [1, 8];
        const choices = CONFIG.DEFAULT_CHOICES;
        
        for (let i = 0; i < 2; i++) {
            const questionNum = questionIndices[i];
            const choice = choices[i];
            
            // 检查问题是否启用
            const enableCheckbox = document.getElementById(`q${questionNum}_enable`);
            if (!enableCheckbox || !enableCheckbox.checked) {
                log(`问题${questionNum}未启用，跳过`);
                continue;
            }
            
            log(`处理问题${questionNum}，选择选项${choice}`);
            
            const block = questionBlocks[i];
            const wrapperOptions = block.querySelectorAll('.ivu-checkbox-wrapper, .ivu-radio-wrapper');
            let optionFound = false;
            
            for (const wrapper of wrapperOptions) {
                for (const node of wrapper.childNodes) {
                    if (node.nodeType === Node.TEXT_NODE && node.textContent.trim() === choice) {
                        optionFound = true;
                        const isAlreadyChecked = wrapper.classList.contains('ivu-checkbox-wrapper-checked') || 
                                               wrapper.classList.contains('ivu-radio-wrapper-checked');
                        
                        if (!isAlreadyChecked) {
                            const inputElement = wrapper.querySelector('input[type="checkbox"], input[type="radio"]');
                            if (inputElement) {
                                // 对于选项输入框，使用dispatchEvent方法
                                const event = new MouseEvent('click', { bubbles: true, cancelable: true });
                                inputElement.dispatchEvent(event);
                                log(`已选择问题${questionNum}的选项${choice}`);
                            }
                        } else {
                            log(`问题${questionNum}的选项${choice}已经选中`);
                        }
                        break;
                    }
                }
                if (optionFound) break;
            }
            
            if (!optionFound) {
                throw new Error(`问题${questionNum}的选项"${choice}"未找到`);
            }
        }
    }
    
    async function waitForConfirmButton() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 15;
            
            const checkForButton = () => {
                const confirmButton = document.querySelector('.ivu-modal-footer .ivu-btn-primary');
                if (confirmButton) {
                    log('找到确认按钮，点击...');
                    confirmButton.click();
                    resolve();
                } else {
                    attempts++;
                    if (attempts >= maxAttempts) {
                        reject(new Error('确认按钮等待超时'));
                    } else {
                        setTimeout(checkForButton, 200);
                    }
                }
            };
            
            checkForButton();
        });
    }
    
    async function processAutoPass() {
        log('自动通过模式: 查找质检通过按钮...');
        
        const passButton = findButtonByText('质检通过') ||
                          findButtonByText('通过') ||
                          Array.from(document.querySelectorAll('button, a'))
                               .find(el => el.textContent.includes('质检通过') ||
                                          el.getAttribute('title')?.includes('质检通过') ||
                                          el.getAttribute('aria-label')?.includes('质检通过'));
        
        if (!passButton) {
            throw new Error('未找到"质检通过"按钮');
        }
        
        log(`找到质检通过按钮: "${passButton.textContent.trim()}"`);
        updateStatus('点击质检通过按钮...', 'info');
        
        // 对于质检通过按钮，使用dispatchEvent方法
        const event = new MouseEvent('click', { bubbles: true, cancelable: true });
        passButton.dispatchEvent(event);
        
        log('质检通过按钮点击完成');
    }
    
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // ===== 自动化控制 =====
    function startAutomation() {
        if (isAutomationRunning) return;

        isAutomationRunning = true;
        updateButtonStates();
        updateStatus('自动化已启动', 'success');
        startMonitoring();
    }

    function stopAutomation() {
        isAutomationRunning = false;
        stopMonitoring();
        updateButtonStates();
        updateStatus('自动化已停止', 'warning');
    }

    function startMonitoring() {
        if (checkTimer) {
            clearInterval(checkTimer);
        }

        checkTimer = setInterval(() => {
            if (!isAutomationRunning) {
                stopMonitoring();
                return;
            }

            checkForTasks();
        }, CONFIG.CHECK_INTERVAL);
    }

    function stopMonitoring() {
        if (checkTimer) {
            clearInterval(checkTimer);
            checkTimer = null;
        }
        if (processTimer) {
            clearTimeout(processTimer);
            processTimer = null;
        }
    }

    function checkForTasks() {
        let targetButton = null;

        if (currentMode === 'modify') {
            targetButton = findButtonByText('修改标注');
        } else {
            targetButton = findButtonByText('质检通过') ||
                          findButtonByText('通过') ||
                          Array.from(document.querySelectorAll('button, a'))
                               .find(el => el.textContent.includes('质检通过') ||
                                          el.getAttribute('title')?.includes('质检通过') ||
                                          el.getAttribute('aria-label')?.includes('质检通过'));
        }

        if (targetButton) {
            stopMonitoring();
            scheduleProcessing();
        }
    }

    function scheduleProcessing() {
        const delay = getRandomDelay();
        const seconds = Math.round(delay / 1000);

        updateStatus(`检测到任务，${seconds}秒后处理...`, 'info');

        processTimer = setTimeout(async () => {
            if (isAutomationRunning) {
                await processTask();
                if (isAutomationRunning) {
                    startMonitoring();
                }
            }
        }, delay);
    }

    function updateButtonStates() {
        if (!continuousButton || !singleButton) return;

        if (isAutomationRunning) {
            continuousButton.innerHTML = '<span class="btn-icon">🛑</span><span class="btn-text">停止处理</span>';
            continuousButton.className = 'button-stop';
            singleButton.disabled = true;
        } else {
            continuousButton.innerHTML = '<span class="btn-icon">🚀</span><span class="btn-text">启动连续处理</span>';
            continuousButton.className = 'button-start';
            singleButton.disabled = false;
        }
    }

    // ===== UI 创建 =====
    function createUI() {
        if (document.getElementById('automation-control-panel')) {
            return;
        }

        log('创建用户界面...');

        const panel = document.createElement('div');
        panel.id = 'automation-control-panel';

        // 拖拽标题栏
        const dragHandle = document.createElement('div');
        dragHandle.id = 'drag-handle';
        dragHandle.innerHTML = `
            <span class="drag-icon">🎯</span>
            <span class="title-text">腾讯质检助手 v19.1</span>
            <span class="drag-hint">⋮⋮</span>
        `;
        panel.appendChild(dragHandle);

        // 模式选择器
        const modeSelector = document.createElement('div');
        modeSelector.className = 'config-section';
        modeSelector.innerHTML = `
            <div id="mode-selector">
                <button id="mode-modify" class="mode-button ${currentMode === 'modify' ? 'active' : ''}" data-mode="modify">
                    <span class="mode-icon">✏️</span>
                    <span class="mode-text">修改标注</span>
                </button>
                <button id="mode-auto-pass" class="mode-button ${currentMode === 'auto-pass' ? 'active' : ''}" data-mode="auto-pass">
                    <span class="mode-icon">✅</span>
                    <span class="mode-text">自动通过</span>
                </button>
            </div>
        `;
        panel.appendChild(modeSelector);

        // 问题配置（仅在修改标注模式下显示）
        const questionConfig = document.createElement('div');
        questionConfig.id = 'question-config';
        questionConfig.className = 'config-section';
        questionConfig.style.display = currentMode === 'modify' ? 'block' : 'none';

        const questionNumbers = [1, 8];
        const choices = CONFIG.DEFAULT_CHOICES;

        for (let i = 0; i < 2; i++) {
            const questionNum = questionNumbers[i];
            const choice = choices[i];

            const questionCard = document.createElement('div');
            questionCard.className = 'question-card';
            questionCard.innerHTML = `
                <div class="question-header">
                    <input type="checkbox" id="q${questionNum}_enable" class="enable-checkbox" checked>
                    <label for="q${questionNum}_enable" class="q-label">
                        <span class="q-number">Q${questionNum}</span>问题${questionNum}
                    </label>
                </div>
                <div class="radio-group">
                    <div class="radio-option ${choice === '1' ? 'selected' : ''}">
                        <input type="radio" id="q${questionNum}_opt1" name="q${questionNum}_options" value="1" ${choice === '1' ? 'checked' : ''}>
                        <label for="q${questionNum}_opt1"><span class="option-badge">选项1</span></label>
                    </div>
                    <div class="radio-option ${choice === '0' ? 'selected' : ''}">
                        <input type="radio" id="q${questionNum}_opt0" name="q${questionNum}_options" value="0" ${choice === '0' ? 'checked' : ''}>
                        <label for="q${questionNum}_opt0"><span class="option-badge">选项0</span></label>
                    </div>
                </div>
            `;
            questionConfig.appendChild(questionCard);
        }
        panel.appendChild(questionConfig);

        // 按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'button-container';

        continuousButton = document.createElement('button');
        continuousButton.id = 'continuous-button';
        continuousButton.className = 'button-start';
        continuousButton.innerHTML = '<span class="btn-icon">🚀</span><span class="btn-text">启动连续处理</span>';
        continuousButton.addEventListener('click', () => {
            if (isAutomationRunning) {
                stopAutomation();
            } else {
                startAutomation();
            }
        });

        singleButton = document.createElement('button');
        singleButton.id = 'single-button';
        singleButton.innerHTML = '<span class="btn-icon">⚡️</span><span class="btn-text">单次处理</span>';
        singleButton.style.display = currentMode === 'modify' ? 'block' : 'none';
        singleButton.addEventListener('click', () => processTask());

        buttonContainer.appendChild(continuousButton);
        buttonContainer.appendChild(singleButton);
        panel.appendChild(buttonContainer);

        // 状态指示器
        statusIndicator = document.createElement('div');
        statusIndicator.id = 'status-indicator';
        statusIndicator.innerHTML = '<span class="status-icon">🎯</span><span class="status-text">待命中</span>';
        panel.appendChild(statusIndicator);

        // 添加事件监听器
        const modifyBtn = panel.querySelector('#mode-modify');
        const autoPassBtn = panel.querySelector('#mode-auto-pass');

        modifyBtn.addEventListener('click', () => setMode('modify'));
        autoPassBtn.addEventListener('click', () => setMode('auto-pass'));

        // 启用拖拽
        enableDragging(panel);

        document.body.appendChild(panel);
        addStyles();

        updateStatus('界面已加载，待命中');
        log('用户界面创建完成');
    }

    function setMode(mode) {
        currentMode = mode;
        GM_setValue('currentMode', mode);

        const modifyBtn = document.querySelector('#mode-modify');
        const autoPassBtn = document.querySelector('#mode-auto-pass');
        const questionConfig = document.querySelector('#question-config');

        if (mode === 'modify') {
            modifyBtn.classList.add('active');
            autoPassBtn.classList.remove('active');
            if (questionConfig) questionConfig.style.display = 'block';
            if (singleButton) singleButton.style.display = 'block';
            updateStatus('切换到修改标注模式', 'success');
        } else {
            modifyBtn.classList.remove('active');
            autoPassBtn.classList.add('active');
            if (questionConfig) questionConfig.style.display = 'none';
            if (singleButton) singleButton.style.display = 'none';
            updateStatus('切换到自动通过模式', 'success');
        }

        log(`模式切换到: ${mode}`);
    }

    function enableDragging(panel) {
        const handle = panel.querySelector('#drag-handle');
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        handle.addEventListener('mousedown', (e) => {
            if (e.target.tagName === 'BUTTON') return;

            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;

            const rect = panel.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;

            panel.style.cursor = 'grabbing';
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            const newLeft = Math.max(0, Math.min(startLeft + deltaX, window.innerWidth - panel.offsetWidth));
            const newTop = Math.max(0, Math.min(startTop + deltaY, window.innerHeight - panel.offsetHeight));

            panel.style.left = newLeft + 'px';
            panel.style.top = newTop + 'px';
            panel.style.right = 'auto';
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                panel.style.cursor = 'grab';
            }
        });
    }

    function addStyles() {
        GM_addStyle(`
            #automation-control-panel {
                position: fixed; top: 80px; right: 15px; z-index: 9999;
                background: linear-gradient(145deg, #ffffff, #f8f9fa);
                border: 1px solid #e3e6ea; border-radius: 12px; padding: 0;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                box-shadow: 0 8px 25px rgba(0,0,0,0.12), 0 4px 10px rgba(0,0,0,0.08);
                width: 280px; cursor: grab; user-select: none;
            }

            #drag-handle {
                background: linear-gradient(135deg, #4f46e5, #7c3aed);
                color: white; padding: 12px 16px; font-size: 13px; font-weight: 600;
                border-top-left-radius: 12px; border-top-right-radius: 12px;
                cursor: grab; display: flex; align-items: center; justify-content: space-between;
            }

            .config-section { padding: 20px; }

            #mode-selector {
                display: flex; justify-content: center; margin-bottom: 16px;
                background-color: #e9ecef; border-radius: 8px; padding: 4px;
            }

            .mode-button {
                flex: 1; padding: 8px 12px; border: none; background-color: transparent;
                color: #495057; font-size: 13px; font-weight: 600; cursor: pointer;
                border-radius: 6px; transition: all 0.2s ease-in-out;
                display: flex; align-items: center; justify-content: center; gap: 6px;
            }

            .mode-button:hover { background-color: #dee2e6; }
            .mode-button.active {
                background-color: #ffffff; color: #4f46e5;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            #question-config { display: flex; flex-direction: column; gap: 16px; }

            .question-card {
                background: linear-gradient(145deg, #f9fafb, #ffffff);
                border: 1px solid #e5e7eb; border-radius: 8px; padding: 12px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            }

            .question-header { display: flex; align-items: center; gap: 8px; margin-bottom: 10px; }
            .enable-checkbox { cursor: pointer; transform: scale(1.2); accent-color: #4f46e5; }

            .q-label {
                font-size: 14px; cursor: pointer; font-weight: 600; color: #374151;
                display: flex; align-items: center; gap: 6px;
            }

            .q-number {
                background: linear-gradient(135deg, #4f46e5, #7c3aed);
                color: white; font-size: 11px; padding: 2px 6px;
                border-radius: 4px; font-weight: 700;
            }

            .radio-group { display: flex; gap: 8px; justify-content: center; }

            .radio-option {
                display: flex; align-items: center; gap: 4px; padding: 6px 12px;
                border-radius: 6px; cursor: pointer; transition: all 0.2s ease;
                border: 1px solid #e5e7eb; background: #ffffff;
            }

            .radio-option:hover { background: #f3f4f6; }
            .radio-option.selected {
                background: linear-gradient(135deg, #ddd6fe, #e0e7ff);
                border-color: #7c3aed;
            }

            .option-badge {
                padding: 2px 8px; border-radius: 4px; font-size: 11px;
                background: #f3f4f6; color: #6b7280; font-weight: 600;
            }

            .radio-option.selected .option-badge { background: #4f46e5; color: white; }

            .button-container {
                padding: 0 20px 16px 20px; display: flex; flex-direction: column; gap: 10px;
            }

            #continuous-button, #single-button {
                color: white; border: none; border-radius: 8px; padding: 12px 16px;
                font-size: 14px; font-weight: 600; cursor: pointer; width: 100%;
                transition: all 0.3s ease; display: flex; align-items: center;
                justify-content: center; gap: 8px;
            }

            .button-start {
                background: linear-gradient(135deg, #10b981, #059669);
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            }

            .button-stop {
                background: linear-gradient(135deg, #ef4444, #dc2626);
                box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
            }

            #single-button {
                background: linear-gradient(135deg, #3b82f6, #2563eb);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }

            #continuous-button:disabled, #single-button:disabled {
                opacity: 0.6; cursor: not-allowed;
            }

            #status-indicator {
                padding: 10px 20px 20px 20px; text-align: center; font-size: 13px;
                font-weight: 600; display: flex; align-items: center; justify-content: center;
                gap: 6px; background: #f9fafb; margin: 0 12px 12px 12px;
                border-radius: 6px; border: 1px solid #e5e7eb;
            }
        `);
    }

    // ===== 程序入口 =====
    function init() {
        log('腾讯质检助手 v19.1 启动');
        log(`当前URL: ${window.location.href}`);
        log(`页面标题: ${document.title}`);

        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startUICheck);
        } else {
            startUICheck();
        }
    }

    function startUICheck() {
        // 定期检查并创建UI
        const uiCheckTimer = setInterval(() => {
            try {
                createUI();
            } catch (error) {
                log(`创建UI出错: ${error.message}`, 'ERROR');
            }
        }, 2000);

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            clearInterval(uiCheckTimer);
            stopAutomation();
            log('应用程序已清理');
        });
    }

    // 启动应用
    try {
        init();
    } catch (error) {
        console.error('[FATAL ERROR v19.1] 应用程序启动失败:', error);
    }

})();
